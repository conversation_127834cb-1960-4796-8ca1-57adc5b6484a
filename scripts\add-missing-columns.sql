-- Add missing columns to products table

-- Add colors column if it doesn't exist
DO $$ 
BEGIN 
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'products' AND column_name = 'colors'
  ) THEN
    ALTER TABLE public.products ADD COLUMN colors TEXT[] DEFAULT '{}';
    RAISE NOTICE 'Added colors column';
  ELSE
    RAISE NOTICE 'Colors column already exists';
  END IF;
END $$;

-- Add sizes column if it doesn't exist
DO $$ 
BEGIN 
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'products' AND column_name = 'sizes'
  ) THEN
    ALTER TABLE public.products ADD COLUMN sizes TEXT[] DEFAULT '{}';
    RAISE NOTICE 'Added sizes column';
  ELSE
    RAISE NOTICE 'Sizes column already exists';
  END IF;
END $$;
