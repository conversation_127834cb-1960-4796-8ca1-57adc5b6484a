// Add missing columns to products table
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addMissingColumns() {
  try {
    console.log('🔧 Adding missing columns to products table...\n');
    
    // SQL to add missing columns
    const alterTableSQL = `
      -- Add colors column if it doesn't exist
      DO $$ 
      BEGIN 
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_name = 'products' AND column_name = 'colors'
        ) THEN
          ALTER TABLE public.products ADD COLUMN colors TEXT[] DEFAULT '{}';
          RAISE NOTICE 'Added colors column';
        ELSE
          RAISE NOTICE 'Colors column already exists';
        END IF;
      END $$;

      -- Add sizes column if it doesn't exist
      DO $$ 
      BEGIN 
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_name = 'products' AND column_name = 'sizes'
        ) THEN
          ALTER TABLE public.products ADD COLUMN sizes TEXT[] DEFAULT '{}';
          RAISE NOTICE 'Added sizes column';
        ELSE
          RAISE NOTICE 'Sizes column already exists';
        END IF;
      END $$;
    `;
    
    console.log('📝 SQL to execute:');
    console.log(alterTableSQL);
    console.log('\n⚠️  Please execute this SQL in your Supabase SQL Editor:');
    console.log('1. Go to your Supabase Dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Copy and paste the SQL above');
    console.log('4. Click "Run"');
    console.log('\nAfter running the SQL, run this script again to verify the changes.');
    
    // Try to check if we can execute SQL directly (this might not work with the client)
    console.log('\n🧪 Attempting to check current schema...');
    
    const { data: products, error } = await supabase
      .from('products')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ Error checking products:', error.message);
    } else if (products && products.length > 0) {
      const hasColors = products[0].hasOwnProperty('colors');
      const hasSizes = products[0].hasOwnProperty('sizes');
      
      console.log(`Colors column exists: ${hasColors ? '✅' : '❌'}`);
      console.log(`Sizes column exists: ${hasSizes ? '✅' : '❌'}`);
      
      if (hasColors && hasSizes) {
        console.log('\n🎉 All required columns are present!');
      } else {
        console.log('\n⚠️  Missing columns detected. Please run the SQL above.');
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

addMissingColumns();
