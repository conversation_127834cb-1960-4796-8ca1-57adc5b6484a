// Check products table schema
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkProductsSchema() {
  try {
    console.log('🔍 Checking products table schema...\n');
    
    // Check if we can query products table
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('*')
      .limit(1);
    
    if (productsError) {
      console.error('❌ Error querying products table:', productsError.message);
      return;
    }
    
    console.log('✅ Products table accessible');
    
    if (products && products.length > 0) {
      console.log('\n📋 Sample product structure:');
      console.log(JSON.stringify(products[0], null, 2));
      
      // Check if colors column exists
      if (products[0].hasOwnProperty('colors')) {
        console.log('\n✅ Colors column exists in products table');
      } else {
        console.log('\n❌ Colors column missing from products table');
      }
      
      // Check if sizes column exists
      if (products[0].hasOwnProperty('sizes')) {
        console.log('✅ Sizes column exists in products table');
      } else {
        console.log('❌ Sizes column missing from products table');
      }
    } else {
      console.log('\n📋 No products found in table');
      
      // Try to insert a test product to see what columns are available
      console.log('\n🧪 Testing product creation with minimal data...');
      
      const testProduct = {
        name: 'Test Product',
        slug: 'test-product-' + Date.now(),
        price: 10.00,
        status: 'active'
      };
      
      const { data: insertData, error: insertError } = await supabase
        .from('products')
        .insert(testProduct)
        .select()
        .single();
      
      if (insertError) {
        console.error('❌ Error inserting test product:', insertError.message);
        console.log('This might indicate missing columns or constraints');
      } else {
        console.log('✅ Test product created successfully');
        console.log('Product structure:', JSON.stringify(insertData, null, 2));
        
        // Clean up test product
        await supabase
          .from('products')
          .delete()
          .eq('id', insertData.id);
        console.log('🧹 Test product cleaned up');
      }
    }
    
  } catch (error) {
    console.error('❌ Schema check error:', error);
  }
}

checkProductsSchema();
