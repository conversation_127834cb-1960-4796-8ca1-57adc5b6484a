-- Migration script to add product variant fields to existing database
-- Run this script if you have an existing database without the variant fields

-- Add colors and sizes columns to products table
ALTER TABLE public.products 
ADD COLUMN IF NOT EXISTS colors TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS sizes TEXT[] DEFAULT '{}';

-- Add comments for documentation
COMMENT ON COLUMN public.products.colors IS 'Array of available colors for this product';
COMMENT ON COLUMN public.products.sizes IS 'Array of available sizes for this product';

-- Update existing products with default variant values if needed
-- This is optional - you can set specific variants for existing products
UPDATE public.products 
SET 
  colors = ARRAY['Black', 'White', 'Gray'],
  sizes = ARRAY['S', 'M', 'L', 'XL']
WHERE colors = '{}' OR colors IS NULL;

-- Create an index on colors and sizes for better query performance
CREATE INDEX IF NOT EXISTS idx_products_colors ON public.products USING GIN (colors);
CREATE INDEX IF NOT EXISTS idx_products_sizes ON public.products USING GIN (sizes);

-- Verify the changes
SELECT 
  id, 
  name, 
  colors, 
  sizes 
FROM public.products 
LIMIT 5;
