'use client'

import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { useState, useEffect } from 'react'
import ProductCard from '@/components/ProductCard'
import JookaHeroDemo from '@/components/ui/jooka-hero-demo'
import FallbackImage from '@/components/ui/FallbackImage'
import { StickyScrollDemo } from '@/components/ui/sticky-scroll-demo'

import TestimonialsSection from '@/components/TestimonialsSection'

// Types
interface Product {
  id: string
  name: string
  price: number
  image: string
  category?: string
  slug?: string
  images?: any[]
  category_name?: string
  status?: string
}

export default function HomePage() {
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch featured products
  useEffect(() => {
    const fetchFeaturedProducts = async () => {
      try {
        setLoading(true)

        // Fetch featured products from API
        const response = await fetch('/api/products?featured=true&status=active&limit=4')
        if (!response.ok) {
          throw new Error('Failed to fetch featured products')
        }
        const result = await response.json()

        // Transform products data for ProductCard component
        const transformedProducts = (result.data || []).map((product: any) => ({
          id: product.id,
          name: product.name,
          price: product.price,
          image: product.images?.[0]?.secure_url || '/placeholder-product.svg',
          category: product.category?.name || product.category_name || 'Uncategorized',
          slug: product.slug,
          images: product.images,
          status: product.status
        }))

        setFeaturedProducts(transformedProducts)

      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
        console.error('Error fetching featured products:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchFeaturedProducts()
  }, [])

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <JookaHeroDemo />

      {/* Featured Products Section */}
      <section className="relative py-24 px-8 md:px-12">
        {/* Clean Background */}
        <div className="absolute inset-0 bg-gradient-to-b from-black via-black/98 to-black" />

        <div className="relative z-10 max-w-7xl mx-auto">
          {/* Section Header */}
          <motion.div
            className="text-center mb-20"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: true }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="inline-block"
            >
              <span className="text-sm font-medium tracking-[0.2em] text-gold/60 uppercase mb-4 block">
                Curated Excellence
              </span>
            </motion.div>

            <motion.h2
              className="text-5xl md:text-6xl lg:text-7xl font-serif font-light text-gold mb-8 tracking-tight"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
            >
              Featured
              <span className="block text-4xl md:text-5xl lg:text-6xl text-ivory/90 font-light italic">
                Collection
              </span>
            </motion.h2>

            <motion.div
              initial={{ opacity: 0, width: 0 }}
              whileInView={{ opacity: 1, width: "4rem" }}
              transition={{ duration: 0.8, delay: 0.5 }}
              viewport={{ once: true }}
              className="h-px bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mb-8"
            />

            <motion.p
              className="text-lg md:text-xl text-ivory/70 max-w-3xl mx-auto leading-relaxed font-light"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              viewport={{ once: true }}
            >
              Discover our carefully curated selection of luxury pieces that embody
              <span className="text-gold/80 font-medium"> timeless elegance</span> and
              <span className="text-gold/80 font-medium"> natural beauty</span>.
            </motion.p>
          </motion.div>

          {/* Products Grid */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            viewport={{ once: true }}
          >
            {loading ? (
              // Loading state
              Array.from({ length: 4 }).map((_, index) => (
                <motion.div
                  key={`loading-${index}`}
                  className="bg-black/40 backdrop-blur-sm border border-gold/10 rounded-lg aspect-[4/5] flex items-center justify-center"
                  initial={{ opacity: 0, y: 60, scale: 0.9 }}
                  whileInView={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{
                    duration: 0.8,
                    delay: 0.9 + index * 0.15,
                    ease: "easeOut"
                  }}
                  viewport={{ once: true }}
                >
                  <div className="w-8 h-8 border-2 border-gold border-t-transparent rounded-full animate-spin"></div>
                </motion.div>
              ))
            ) : error ? (
              // Error state
              <motion.div
                className="col-span-full text-center py-12"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <p className="text-red-400 mb-4">Error loading featured products: {error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="px-6 py-2 bg-gold text-black rounded hover:bg-gold/80 transition-colors"
                >
                  Retry
                </button>
              </motion.div>
            ) : featuredProducts.length === 0 ? (
              // No products state
              <motion.div
                className="col-span-full text-center py-12"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <p className="text-ivory/60 mb-4">No featured products available</p>
                <Link
                  href="/shop"
                  className="px-6 py-2 bg-gold text-black rounded hover:bg-gold/80 transition-colors"
                >
                  Browse All Products
                </Link>
              </motion.div>
            ) : (
              // Products loaded successfully
              featuredProducts.map((product, index) => (
                <motion.div
                  key={product.id}
                  initial={{ opacity: 0, y: 60, scale: 0.9 }}
                  whileInView={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{
                    duration: 0.8,
                    delay: 0.9 + index * 0.15,
                    ease: "easeOut"
                  }}
                  viewport={{ once: true }}
                  className="group"
                >
                  <ProductCard product={product} />
                </motion.div>
              ))
            )}
          </motion.div>

          {/* Call to Action */}
          <motion.div
            className="text-center mt-20"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.4 }}
            viewport={{ once: true }}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              <Link
                href="/shop"
                className="inline-flex items-center px-8 py-4 text-sm font-medium tracking-[0.1em] text-gold border-2 border-gold/30 hover:border-gold hover:bg-gold hover:text-black transition-all duration-500 uppercase group"
              >
                <span>Shop Now</span>
                <motion.span
                  className="ml-2 group-hover:ml-4 transition-all duration-300"
                  initial={{ x: 0 }}
                  whileHover={{ x: 4 }}
                >
                  →
                </motion.span>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>


      {/* Our Story - Sticky Scroll Section */}
      <StickyScrollDemo />

      {/* Testimonials Section */}
      <TestimonialsSection />
    </div>
  )
}