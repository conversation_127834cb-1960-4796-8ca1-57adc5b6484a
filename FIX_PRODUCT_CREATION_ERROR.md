# Fix Product Creation Error - Missing Columns

## Problem
The product creation form shows the error: "Could not find the 'colors' column of 'products' in the schema cache"

## Root Cause
The database schema is missing the `colors` and `sizes` columns that are required by the product creation API.

## Solution

### Step 1: Access Supabase Dashboard
1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**

### Step 2: Execute the Following SQL

Copy and paste this SQL code into the SQL Editor and click **Run**:

```sql
-- Add missing columns to products table

-- Add colors column if it doesn't exist
DO $$ 
BEGIN 
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'products' AND column_name = 'colors'
  ) THEN
    ALTER TABLE public.products ADD COLUMN colors TEXT[] DEFAULT '{}';
    RAISE NOTICE 'Added colors column';
  ELSE
    RAISE NOTICE 'Colors column already exists';
  END IF;
END $$;

-- Add sizes column if it doesn't exist
DO $$ 
BEGIN 
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'products' AND column_name = 'sizes'
  ) THEN
    ALTER TABLE public.products ADD COLUMN sizes TEXT[] DEFAULT '{}';
    RAISE NOTICE 'Added sizes column';
  ELSE
    RAISE NOTICE 'Sizes column already exists';
  END IF;
END $$;
```

### Step 3: Verify the Fix

After executing the SQL, run this command to verify the columns were added:

```bash
node scripts/check-products-schema.js
```

You should see:
- ✅ Colors column exists in products table
- ✅ Sizes column exists in products table

### Step 4: Test Product Creation

1. Go to `http://localhost:3001/admin/products`
2. Click "Create New Product"
3. Fill out the form and try to create a product
4. The error should be resolved

## Alternative: Manual Column Addition

If the above SQL doesn't work, you can add the columns manually:

```sql
ALTER TABLE public.products ADD COLUMN colors TEXT[] DEFAULT '{}';
ALTER TABLE public.products ADD COLUMN sizes TEXT[] DEFAULT '{}';
```

## What These Columns Do

- **colors**: Stores an array of available colors for the product (e.g., ['Red', 'Blue', 'Green'])
- **sizes**: Stores an array of available sizes for the product (e.g., ['S', 'M', 'L', 'XL'])

Both columns are required by the product creation API and are used for product variants.

## Next Steps

Once this is fixed, the product creation workflow will function properly and new products will:
1. Be created successfully in the database
2. Automatically appear on the shop page
3. Show in featured products section if marked as featured
