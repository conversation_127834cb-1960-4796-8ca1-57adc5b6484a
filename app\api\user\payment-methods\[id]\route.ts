// Individual payment method management API routes
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/middleware';
import { db } from '@/lib/database/index';

async function deletePaymentMethod(request: NextRequest, { params }: { params: { id: string } }) {
  const paymentMethodId = params.id;
  
  try {
    const result = await db.deletePaymentMethod(paymentMethodId);
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({ message: 'Payment method deleted successfully' });
  } catch (error) {
    console.error('Delete payment method error:', error);
    return NextResponse.json(
      { error: 'Failed to delete payment method' },
      { status: 500 }
    );
  }
}

export const DELETE = withAuth(deletePaymentMethod);