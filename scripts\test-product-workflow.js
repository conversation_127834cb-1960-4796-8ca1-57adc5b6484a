// Test the complete product creation workflow
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testProductWorkflow() {
  console.log('🧪 Testing Product Creation Workflow...\n');
  
  try {
    // Step 1: Check if colors and sizes columns exist
    console.log('Step 1: Checking database schema...');
    const { data: products, error: schemaError } = await supabase
      .from('products')
      .select('*')
      .limit(1);
    
    if (schemaError) {
      console.error('❌ Schema check failed:', schemaError.message);
      return;
    }
    
    if (products && products.length > 0) {
      const hasColors = products[0].hasOwnProperty('colors');
      const hasSizes = products[0].hasOwnProperty('sizes');
      
      console.log(`Colors column: ${hasColors ? '✅' : '❌'}`);
      console.log(`Sizes column: ${hasSizes ? '✅' : '❌'}`);
      
      if (!hasColors || !hasSizes) {
        console.log('\n⚠️  Missing required columns. Please run the database fix first:');
        console.log('See FIX_PRODUCT_CREATION_ERROR.md for instructions');
        return;
      }
    }
    
    // Step 2: Test product creation via API
    console.log('\nStep 2: Testing product creation via API...');
    
    const testProduct = {
      name: 'Test Product ' + Date.now(),
      slug: 'test-product-' + Date.now(),
      description: 'This is a test product created by the workflow test',
      shortDescription: 'Test product for workflow verification',
      price: 99.99,
      comparePrice: 129.99,
      inventoryCount: 10,
      status: 'active',
      featured: true,
      images: [],
      colors: ['Red', 'Blue'],
      sizes: ['M', 'L'],
      tags: ['test']
    };
    
    const createResponse = await fetch('http://localhost:3001/api/products', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testProduct),
    });
    
    if (!createResponse.ok) {
      const errorData = await createResponse.json();
      console.error('❌ Product creation failed:', errorData);
      return;
    }
    
    const createResult = await createResponse.json();
    console.log('✅ Product created successfully:', createResult.data.name);
    const productId = createResult.data.id;
    
    // Step 3: Verify product appears in shop page API
    console.log('\nStep 3: Testing shop page API...');
    
    const shopResponse = await fetch('http://localhost:3001/api/products?status=active');
    if (!shopResponse.ok) {
      console.error('❌ Shop API failed');
      return;
    }
    
    const shopResult = await shopResponse.json();
    const foundInShop = shopResult.data.some(p => p.id === productId);
    console.log(`Product appears in shop: ${foundInShop ? '✅' : '❌'}`);
    
    // Step 4: Verify featured product appears in featured API
    console.log('\nStep 4: Testing featured products API...');
    
    const featuredResponse = await fetch('http://localhost:3001/api/products?featured=true&status=active');
    if (!featuredResponse.ok) {
      console.error('❌ Featured API failed');
      return;
    }
    
    const featuredResult = await featuredResponse.json();
    const foundInFeatured = featuredResult.data.some(p => p.id === productId);
    console.log(`Product appears in featured: ${foundInFeatured ? '✅' : '❌'}`);
    
    // Step 5: Test product update
    console.log('\nStep 5: Testing product update...');
    
    const updateResponse = await fetch(`http://localhost:3001/api/products/${productId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        featured: false,
        status: 'inactive'
      }),
    });
    
    if (!updateResponse.ok) {
      console.error('❌ Product update failed');
    } else {
      console.log('✅ Product updated successfully');
    }
    
    // Step 6: Cleanup - delete test product
    console.log('\nStep 6: Cleaning up test product...');
    
    const { error: deleteError } = await supabase
      .from('products')
      .delete()
      .eq('id', productId);
    
    if (deleteError) {
      console.error('❌ Cleanup failed:', deleteError.message);
    } else {
      console.log('✅ Test product cleaned up');
    }
    
    console.log('\n🎉 Product workflow test completed successfully!');
    console.log('\n📋 Summary:');
    console.log('- Database schema is correct');
    console.log('- Product creation API works');
    console.log('- Products appear on shop page');
    console.log('- Featured products functionality works');
    console.log('- Product updates work');
    
  } catch (error) {
    console.error('❌ Workflow test failed:', error);
  }
}

testProductWorkflow();
