import { NextRequest, NextResponse } from 'next/server';
import { createServiceClient } from '@/lib/supabase/service';

export async function POST(request: NextRequest) {
  try {
    const supabase = createServiceClient();
    
    console.log('Creating sample orders...');
    
    // Get existing users and products
    const { data: users } = await supabase
      .from('users')
      .select('id, email')
      .eq('role', 'customer')
      .limit(1);
    
    const { data: products } = await supabase
      .from('products')
      .select('id, name, price')
      .eq('status', 'active')
      .limit(3);
    
    console.log('Found users:', users?.length || 0);
    console.log('Found products:', products?.length || 0);
    
    if (!users || users.length === 0) {
      return NextResponse.json({
        error: 'No customer users found. Please create users first.'
      }, { status: 400 });
    }
    
    if (!products || products.length === 0) {
      return NextResponse.json({
        error: 'No products found. Please create products first.'
      }, { status: 400 });
    }
    
    const customer = users[0];
    let ordersCreated = 0;
    const orderResults = [];
    
    // Create 5 sample orders
    for (let i = 1; i <= 5; i++) {
      try {
        const orderDate = new Date();
        orderDate.setDate(orderDate.getDate() - i);
        
        const subtotal = 100 + (i * 50);
        const taxAmount = Math.round(subtotal * 0.1 * 100) / 100;
        const shippingAmount = 9.99;
        const totalAmount = Math.round((subtotal + taxAmount + shippingAmount) * 100) / 100;
        
        const orderData = {
          order_number: `JOO2025073${String(i).padStart(2, '0')}${String(Math.floor(Math.random() * 100)).padStart(2, '0')}`,
          user_id: customer.id,
          email: customer.email,
          status: i === 1 ? 'pending' : i === 2 ? 'processing' : 'delivered',
          subtotal: subtotal,
          tax_amount: taxAmount,
          shipping_amount: shippingAmount,
          total_amount: totalAmount,
          shipping_address: {
            first_name: 'John',
            last_name: 'Doe',
            address_line_1: '123 Main St',
            city: 'New York',
            state: 'NY',
            postal_code: '10001',
            country: 'US'
          },
          billing_address: {
            first_name: 'John',
            last_name: 'Doe',
            address_line_1: '123 Main St',
            city: 'New York',
            state: 'NY',
            postal_code: '10001',
            country: 'US'
          },
          payment_method: {
            type: 'credit_card',
            last_four: '4242',
            brand: 'visa'
          },
          created_at: orderDate.toISOString(),
          updated_at: orderDate.toISOString()
        };
        
        console.log(`Creating order ${i}:`, orderData);
        
        const { data: order, error: orderError } = await supabase
          .from('orders')
          .insert(orderData)
          .select()
          .single();
        
        if (orderError) {
          console.error(`Order ${i} creation error:`, orderError);
          orderResults.push({ order: i, error: orderError.message });
          continue;
        }
        
        console.log(`Order ${i} created successfully:`, order.id);
        ordersCreated++;
        orderResults.push({ order: i, success: true, id: order.id });
        
        // Add order items
        const product = products[i % products.length];
        const quantity = Math.floor(Math.random() * 3) + 1;
        const unitPrice = product.price;
        const totalPrice = Math.round(quantity * unitPrice * 100) / 100;
        
        const { data: item, error: itemError } = await supabase
          .from('order_items')
          .insert({
            order_id: order.id,
            product_id: product.id,
            quantity: quantity,
            unit_price: unitPrice,
            total_price: totalPrice
          })
          .select()
          .single();
        
        if (itemError) {
          console.error(`Order item ${i} creation error:`, itemError);
          // orderResults[orderResults.length - 1].itemError = itemError.message;
        } else {
          console.log(`Order item ${i} created successfully`);
          // orderResults[orderResults.length - 1].itemCreated = true;
        }
        
      } catch (err) {
        console.error(`Error creating order ${i}:`, err);
        orderResults.push({ 
          order: i, 
          error: err instanceof Error ? err.message : 'Unknown error' 
        });
      }
    }
    
    // Test the dashboard functions
    console.log('Testing dashboard functions...');
    const { data: salesData, error: salesError } = await supabase.rpc('get_sales_analytics');
    const { data: lowStockData, error: lowStockError } = await supabase.rpc('get_low_stock_products', { threshold: 10 });
    
    // Get final counts
    const { count: orderCount } = await supabase.from('orders').select('*', { count: 'exact', head: true });
    const { count: itemCount } = await supabase.from('order_items').select('*', { count: 'exact', head: true });
    
    return NextResponse.json({
      success: true,
      message: `Created ${ordersCreated} orders successfully`,
      data: {
        ordersCreated,
        orderResults,
        finalCounts: {
          orders: orderCount || 0,
          orderItems: itemCount || 0
        },
        functions: {
          salesAnalytics: {
            data: salesData,
            error: salesError?.message || null
          },
          lowStockProducts: {
            count: lowStockData?.length || 0,
            error: lowStockError?.message || null
          }
        }
      }
    });
    
  } catch (error) {
    console.error('Create orders error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create orders', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
