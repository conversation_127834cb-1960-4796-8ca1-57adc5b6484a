// Real-time client for Supabase subscriptions
import { createClient } from '@/lib/supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';

export class RealtimeClient {
  private supabase = createClient();
  private channels: Map<string, RealtimeChannel> = new Map();

  // Subscribe to inventory updates
  subscribeToInventoryUpdates(callback: (payload: any) => void) {
    const channel = this.supabase
      .channel('inventory-updates')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'products',
          filter: 'track_inventory=eq.true',
        },
        (payload: any) => {
          console.log('Inventory update:', payload);
          callback(payload);
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'inventory_transactions',
        },
        (payload: any) => {
          console.log('Inventory transaction:', payload);
          callback(payload);
        }
      )
      .subscribe();

    this.channels.set('inventory-updates', channel);
    return channel;
  }

  // Subscribe to order status updates
  subscribeToOrderUpdates(userId: string, callback: (payload: any) => void) {
    const channel = this.supabase
      .channel(`order-updates-${userId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'orders',
          filter: `user_id=eq.${userId}`,
        },
        (payload: any) => {
          console.log('Order update:', payload);
          callback(payload);
        }
      )
      .subscribe();

    this.channels.set(`order-updates-${userId}`, channel);
    return channel;
  }

  // Subscribe to admin order notifications
  subscribeToAdminOrderNotifications(callback: (payload: any) => void) {
    const channel = this.supabase
      .channel('admin-order-notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'orders',
        },
        (payload: any) => {
          console.log('New order notification:', payload);
          callback(payload);
        }
      )
      .subscribe();

    this.channels.set('admin-order-notifications', channel);
    return channel;
  }

  // Subscribe to low stock alerts
  subscribeToLowStockAlerts(threshold: number, callback: (payload: any) => void) {
    const channel = this.supabase
      .channel('low-stock-alerts')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'products',
          filter: `inventory_count=lte.${threshold}`,
        },
        (payload: any) => {
          console.log('Low stock alert:', payload);
          callback(payload);
        }
      )
      .subscribe();

    this.channels.set('low-stock-alerts', channel);
    return channel;
  }

  // Subscribe to user notifications
  subscribeToUserNotifications(userId: string, callback: (payload: any) => void) {
    const channel = this.supabase
      .channel(`user-notifications-${userId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`,
        },
        (payload: any) => {
          console.log('New notification:', payload);
          callback(payload);
        }
      )
      .subscribe();

    this.channels.set(`user-notifications-${userId}`, channel);
    return channel;
  }

  // Unsubscribe from a specific channel
  unsubscribe(channelName: string) {
    const channel = this.channels.get(channelName);
    if (channel) {
      this.supabase.removeChannel(channel);
      this.channels.delete(channelName);
    }
  }

  // Unsubscribe from all channels
  unsubscribeAll() {
    this.channels.forEach((channel, name) => {
      this.supabase.removeChannel(channel);
    });
    this.channels.clear();
  }

  // Get channel status
  getChannelStatus(channelName: string) {
    const channel = this.channels.get(channelName);
    return channel ? channel.state : 'not_subscribed';
  }

  // Send real-time message to a channel
  async sendMessage(channelName: string, event: string, payload: any) {
    const channel = this.channels.get(channelName);
    if (channel) {
      return await channel.send({
        type: 'broadcast',
        event,
        payload,
      });
    }
    throw new Error(`Channel ${channelName} not found`);
  }
}

// Export singleton instance
export const realtimeClient = new RealtimeClient();